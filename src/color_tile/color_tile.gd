class_name ColorTile
extends Node2D

signal painted
signal erased
signal paint_requested(tile: ColorTile)

@export var color_rect: ColorRect
@export var paint_area: Area2D

func _ready() -> void:
	add_to_group("tiles")
	paint_area.area_entered.connect(_on_area_entered)

func paint(color: Color) -> void:
	if is_painted():
		return

	color_rect.color = color
	color_rect.show()

	painted.emit()

func is_painted() -> bool:
	return color_rect.visible

func unpaint() -> void:
	if not is_painted():
		return

	color_rect.hide()
	erased.emit()

func _on_area_entered(area: Area2D) -> void:
	if area is InteractionTrigger:
		paint_requested.emit(self)
