**Название игры:** (Рабочее название: Fruit Rogue)

**Жанр:** Аркадный рогалик с механиками контроля территории и менеджментом ресурсов.

**Ключевые геймплейные механики:**

1.  **Система Ресурсов:**
    *   **Краска (Топливо):** Основной расходуемый ресурс, находящийся в "баке" игрока. Тратится по 1 единице за каждый закрашенный тайл. Также может расходоваться на способности от некоторых артефактов. Восполняется подбором капель краски на уровне.
    *   **Закрашенные Тайлы (Валюта):** Накопительный счетчик всех уникальных тайлов, закрашенных игроком за текущий забег. Это единственная валюта, которая тратится в магазинах на покупку артефактов.

2.  **Покраска и Цель:**
    *   При перемещении на незакрашенную клетку, игрок **тратит 1 единицу Краски (Топлива)**, чтобы ее закрасить. После этого счетчик валюты "Закрашенные Тайлы" увеличивается на 1. Если у игрока нет Краски (Топлива), он не может закрашивать новые тайлы.
    *   Цель в каждой "боевой" комнате — закрасить 100% доступных для покраски тайлов, чтобы открыть выход.

3.  **Взаимодействие с врагами (Пилами):**
    *   Базовый геймплей построен на уклонении от движущихся пил.
    *   Существуют разные типы пил с разным поведением.

**Система артефактов:**

Игрок начинает каждый забег без артефактов. Все способности, баффы и пассивные эффекты приобретаются во время забега в виде артефактов.

**Артефакты:**

*   **"Фазовый сдвиг" (WallPhaseAbilityData):** Позволяет игроку проходить сквозь стены. При попытке движения в стену тратится Краска (Топливо), и игрок перемещается до первой свободной клетки в этом направлении.
*   **"Переработка чернил" (RestorePaintAbilityData):** Восстанавливает 1 единицу Краски (Топлива) каждый раз, когда игрок наступает на уже закрашенную клетку.
*   **"Рог изобилия" (MultiDropAbilityData):** Увеличивает количество капель краски, одновременно присутствующих на уровне.
*   **"Стабилизатор времени" (MaintainNormalTimeAbilityData):** Предотвращает ускорение времени, когда игрок движется по уже закрашенным клеткам. Враги остаются замедленными, позволяя безопасно маневрировать по своей территории.
*   **"Нескончаемая краска" (Paint Streak):** После закрашивания определенного количества клеток подряд, покраска перестает расходовать краску до тех пор, пока игрок не наступит на уже закрашенную клетку.
*   **"Телепорт по краске" (Painted Teleport):** Позволяет игроку, находясь на закрашенной клетке, телепортироваться сквозь стены (или с края на край карты) к следующей закрашенной клетке в направлении движения. Эта способность расходует краску.


**Основной геймплейный цикл (Core Gameplay Loop):**

1.  **Вход в комнату:** Игрок появляется в комнате с врагами, каплями краски и незакрашенными тайлами.
2.  **Фаза менеджмента и покраски:** Игрок маневрирует между врагами, тратя **Краску (Топливо)** для покраски тайлов, что генерирует ему **Валюту**. Он должен следить за запасом Топлива и вовремя подбирать капли краски.
3.  **Зачистка комнаты:** Игрок закрашивает 100% тайлов, и выход открывается.
4.  **Переход:** Игрок переходит в следующую комнату (боевая, магазин, босс).
5.  **Магазин:** В комнатах-магазинах игрок тратит накопленную **Валюту ("Закрашенные Тайлы")** на покупку артефактов.
6.  **Повторение:** Цикл повторяется до встречи с боссом.

**Система прогрессии игрока:**

*   **Прогрессия внутри забега:** Построена исключительно на сборе и комбинировании артефактов. Игрок формирует уникальный билд способностей для каждого забега.
*   **Мета-прогрессия (между забегами):**
    *   После любого поражения (смерти игрока) **все заработанные за забег "Закрашенные Тайлы"** автоматически добавляются к мета-валюте.
    *   В хабе (вне забега) мета-валюта тратится на разблокировку новых артефактов, которые навсегда добавляются в пул доступных артефактов для будущих забегов.

**Уникальные особенности геймплея (USP):**

*   **Чистый билд-ориентированный геймплей:** Прогрессия в забеге зависит исключительно от выбора артефактов и их синергии, а не от числовых улучшений.
*   **Напряжение экономики "Сейчас или Потом":** Потратив валюту на артефакты для усиления в текущем забеге, игрок уменьшает количество валюты, которую сможет конвертировать в постоянную мета-прогрессию в конце.
