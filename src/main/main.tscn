[gd_scene load_steps=10 format=3 uid="uid://cepew8eycqnwb"]

[ext_resource type="Script" uid="uid://6bq5j2lpieqg" path="res://src/main/main.gd" id="1_3s6yk"]
[ext_resource type="PackedScene" uid="uid://bqx8h4n5m2k7p" path="res://src/hub/hub.tscn" id="2_ab7mr"]
[ext_resource type="Script" uid="uid://dfpgio0c1duyy" path="res://src/main/game_orchestrator.gd" id="2_orchestrator"]
[ext_resource type="PackedScene" uid="uid://ddu485x3w8hll" path="res://src/levels/level_1/level_1_1.tscn" id="4_30cu5"]
[ext_resource type="PackedScene" uid="uid://cvxssim0a0xvw" path="res://src/levels/level_1/level_1_2.tscn" id="5_frqgp"]
[ext_resource type="PackedScene" uid="uid://bbv3tfaswcfge" path="res://src/levels/level_1/level_1_3.tscn" id="6_2nke2"]
[ext_resource type="PackedScene" uid="uid://d1sj0qg3f2fo8" path="res://src/levels/level_1/level_1_4.tscn" id="7_km7bn"]
[ext_resource type="PackedScene" uid="uid://cxhc4tig2eklx" path="res://src/player/characters/apple.tscn" id="8_apple"]
[ext_resource type="PackedScene" uid="uid://cvmn3d8forhd5" path="res://src/shop/shop_level.tscn" id="9_shop"]

[node name="Main" type="Node" node_paths=PackedStringArray("orchestrator", "scene_container")]
script = ExtResource("1_3s6yk")
initial_scene = ExtResource("2_ab7mr")
orchestrator = NodePath("GameOrchestrator")
scene_container = NodePath("SceneContainer")

[node name="GameOrchestrator" type="Node" parent="."]
script = ExtResource("2_orchestrator")
hub_scene = ExtResource("2_ab7mr")
run_levels = Array[PackedScene]([ExtResource("7_km7bn"), ExtResource("4_30cu5"), ExtResource("5_frqgp"), ExtResource("6_2nke2")])
player_scene = ExtResource("8_apple")
shop_scene = ExtResource("9_shop")

[node name="SceneContainer" type="Node" parent="."]
