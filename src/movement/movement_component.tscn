[gd_scene load_steps=4 format=3 uid="uid://k2v1icx8llrc"]

[ext_resource type="Script" uid="uid://bpmyflh277fbw" path="res://src/movement/movement_component.gd" id="1_ixeon"]
[ext_resource type="Script" uid="uid://c1gtewldstgdg" path="res://src/movement/movement_data.gd" id="2_1ryje"]

[sub_resource type="Resource" id="Resource_dchgf"]
script = ExtResource("2_1ryje")
move_duration = 0.2
metadata/_custom_type_script = "uid://c1gtewldstgdg"

[node name="MovementComponent" type="Node" node_paths=PackedStringArray("collision_ray")]
script = ExtResource("1_ixeon")
data = SubResource("Resource_dchgf")
collision_ray = NodePath("")
