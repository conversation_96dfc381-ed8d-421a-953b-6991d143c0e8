extends Node

const TILE_SIZE: int = 8

var _effective_stats: Dictionary = {}
var _paint_color: Color
var _initial_repeat_delay: float
var _repeat_rate: float

func initialize_for_run(player_stats_component: StatsComponent) -> void:
	_effective_stats.clear()
	var base_stats_data: PlayerBaseStatsData = player_stats_component.base_stats

	var all_upgradeable_stats: Array[BaseUpgradeableStatData] = GameProgress.get_upgradeable_stats()

	for stat_data in all_upgradeable_stats:
		var stat_id: StringName = stat_data.stat_id
		var base_value: Variant = base_stats_data.get_stat_value(stat_id)
		var upgrade_level: int = GameProgress.get_stat_upgrade_level(stat_id)
		var final_value: Variant = stat_data.calculate_value(base_value, upgrade_level)
		_effective_stats[stat_id] = final_value

	_paint_color = base_stats_data.paint_color
	_initial_repeat_delay = base_stats_data.initial_repeat_delay
	_repeat_rate = base_stats_data.repeat_rate

func spend_extra_life() -> bool:
	var current_extra_lives: int = _effective_stats.get("extra_lives", 0)
	if current_extra_lives > 0:
		_effective_stats["extra_lives"] = current_extra_lives - 1
		return true
	return false

func get_max_health() -> int:
	if not _effective_stats.has("max_health"):
		push_error("Статистика max_health не найдена в _effective_stats")
		return 0
	return _effective_stats["max_health"]

func get_max_paint() -> int:
	if not _effective_stats.has("max_paint"):
		push_error("Статистика max_paint не найдена в _effective_stats")
		return 0
	return _effective_stats["max_paint"]

func get_move_duration() -> float:
	if not _effective_stats.has("move_duration"):
		push_error("Статистика move_duration не найдена в _effective_stats")
		return 0.0
	var duration: float = _effective_stats["move_duration"]
	return max(0.05, duration)

func get_extra_lives() -> int:
	if not _effective_stats.has("extra_lives"):
		push_error("Статистика extra_lives не найдена в _effective_stats")
		return 0
	return _effective_stats["extra_lives"]

func get_size_modifier() -> float:
	if not _effective_stats.has("size_modifier"):
		push_error("Статистика size_modifier не найдена в _effective_stats")
		return 0.0
	return _effective_stats["size_modifier"]

func get_paint_color() -> Color:
	return _paint_color

func get_tile_size() -> int:
	return TILE_SIZE

func get_initial_repeat_delay() -> float:
	return _initial_repeat_delay

func get_repeat_rate() -> float:
	return _repeat_rate
