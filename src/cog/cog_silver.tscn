[gd_scene load_steps=11 format=3 uid="uid://cginrjmepqtfq"]

[ext_resource type="Script" uid="uid://dy4aa6cofjgyh" path="res://src/cog/cog.gd" id="1_sqljv"]
[ext_resource type="Texture2D" uid="uid://dunym6c7vtv8i" path="res://assets/cog_silver.png" id="2_xdquv"]
[ext_resource type="PackedScene" uid="uid://cn0cllvcdbuiy" path="res://src/damage/damage_component.tscn" id="3_mrmkw"]
[ext_resource type="Script" uid="uid://m48gui61bik0" path="res://src/damage/damage_data.gd" id="3_mxeri"]
[ext_resource type="Script" uid="uid://clrphpo8v8ep1" path="res://src/time_dilation/time_sensitive_data.gd" id="4_time_data"]
[ext_resource type="PackedScene" uid="uid://db6e8chc7nyk3" path="res://src/time_dilation/time_sensitive_component.tscn" id="4_time_sensitive"]

[sub_resource type="Resource" id="TimeSensitiveData_abcde"]
script = ExtResource("4_time_data")
slow_speed = 75.0
normal_speed = 450.0
slow_rotation_speed = 1.5
normal_rotation_speed = 15.0

[sub_resource type="Resource" id="Resource_71rs4"]
script = ExtResource("3_mxeri")
amount = 1
cooldown = 1.0
area_path = NodePath("DamageArea")
metadata/_custom_type_script = "uid://m48gui61bik0"

[sub_resource type="CircleShape2D" id="CircleShape2D_xdquv"]
radius = 3.0

[sub_resource type="CircleShape2D" id="CircleShape2D_mxeri"]
radius = 3.31

[node name="CogSilver" type="CharacterBody2D" node_paths=PackedStringArray("sprite", "time_sensitive_component")]
collision_layer = 2
collision_mask = 5
motion_mode = 1
script = ExtResource("1_sqljv")
sprite = NodePath("Sprite2D")
time_sensitive_component = NodePath("TimeSensitiveComponent")

[node name="TimeSensitiveComponent" parent="." instance=ExtResource("4_time_sensitive")]
data = SubResource("TimeSensitiveData_abcde")

[node name="DamageDealerComponent" parent="." instance=ExtResource("3_mrmkw")]
data = SubResource("Resource_71rs4")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("2_xdquv")

[node name="Collision" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_xdquv")

[node name="DamageArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="DamageArea"]
shape = SubResource("CircleShape2D_mxeri")
