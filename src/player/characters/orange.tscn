[gd_scene load_steps=4 format=3 uid="uid://40v80kunrtu1"]

[ext_resource type="PackedScene" uid="uid://dq0bjiqkyl4wv" path="res://src/player/player.tscn" id="1_itthl"]
[ext_resource type="Resource" uid="uid://c6o5y5cbns7mi" path="res://resources/characters/orange_stats.tres" id="2_ww5h6"]
[ext_resource type="Texture2D" uid="uid://dia1elmq6p4y" path="res://assets/orange.png" id="6_o46ri"]

[node name="Orange" instance=ExtResource("1_itthl")]

[node name="StatsComponent" parent="." index="0"]
base_stats = ExtResource("2_ww5h6")

[node name="Sprite2D" parent="." index="7"]
texture = ExtResource("6_o46ri")

[node name="Collision" parent="." index="8"]
position = Vector2(0, -4)

[node name="Hitbox" parent="." index="9"]
position = Vector2(-1, -4)

[node name="InteractionArea" parent="." index="11"]
script = null
