class_name WaterTileComponent
extends Node2D

signal entered(tile: WaterTileComponent)
signal exited(tile: WaterTileComponent)

@export var data: WaterTileData
@export var area: Area2D

func _ready() -> void:
	add_to_group("water_tiles")
	add_to_group("tiles")
	area.area_entered.connect(_on_area_entered)
	area.area_exited.connect(_on_area_exited)

func _on_area_entered(area_entered: Area2D) -> void:
	if area_entered is InteractionTrigger:
		entered.emit(self)

func _on_area_exited(area_exited: Area2D) -> void:
	if area_exited is InteractionTrigger:
		exited.emit(self)
